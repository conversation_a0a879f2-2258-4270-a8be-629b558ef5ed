@model OrderFlowCore.Web.ViewModels.OrderNewViewModel
@{
    ViewData["Title"] = "تقديم طلب جديد / Submit New Order";
    Layout = "_Layout";
}

<!-- Hero Section -->
<section class="hero-contact">
    <div class="container text-center text-white position-relative">
        <h1 class="display-3 fw-bold mb-4 animate-fade-in">تقديم طلب جديد / Submit New Order</h1>
        <p class="lead fs-4 mb-0 animate-fade-in" style="animation-delay: 0.2s;">املأ النموذج أدناه لتقديم طلبك بسهولة وسرعة / Fill out the form below to submit your request easily and quickly</p>

        <!-- Floating shapes -->
        <div class="floating-shape"
            style="top: 20%; right: 10%; width: 100px; height: 100px; background: radial-gradient(circle, rgba(255,255,255,0.2) 0%, transparent 70%); border-radius: 50%;">
        </div>
        <div class="floating-shape"
            style="bottom: 30%; left: 5%; width: 80px; height: 80px; background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, transparent 70%); border-radius: 50%; animation-delay: -3s;">
        </div>
    </div>
</section> 

<!-- Order Form Section -->
<section class="py-5" style="margin-top: -150px;">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="contact-form-card p-5">
                    <div class="text-center mb-5">
                        <span class="badge bg-primary bg-opacity-10 text-primary px-3 py-2 mb-3">طلب جديد / New Order</span>
                        <h2 class="display-6 fw-bold">معلومات الموظف / Employee Information</h2>
                        <p class="text-muted">يرجى ملء جميع الحقول المطلوبة بدقة / Please fill all required fields accurately</p>
                    </div>

                    <!-- Employee Search Section -->
                    <div class="card mb-4 border-0 shadow-sm">
                        <div class="card-body">
                            <div class="text-center mb-3">
                                <h6 class="fw-bold text-primary">
                                    <i class="fas fa-search me-2"></i>البحث عن الموظف / Search for Employee
                                </h6>
                                <p class="text-muted small">أدخل السجل المدني للبحث عن بيانات الموظف / Enter civil record to search for employee data</p>
                            </div>
                            <div class="row g-3 align-items-end">
                                <div class="col-md-8">
                                    <label class="form-label-modern-dark small">السجل المدني / Civil Record</label>
                                    <input type="text" id="searchCivilRecord" class="form-control form-control-modern"
                                        placeholder="أدخل السجل المدني / Enter civil record" />
                                </div>
                                <div class="col-md-4">
                                    <button type="button" id="searchEmployeeBtn"
                                        class="btn btn-modern btn-primary-modern w-100">
                                        <i class="fas fa-search me-2"></i>بحث / Search
                                    </button>
                                </div>
                            </div>
                            <div id="searchResult" class="mt-3"></div>
                        </div>
                    </div>

                    @if (TempData["OrderSuccess"] != null && TempData["OrderId"] != null)

                    {
                        <div class="success-message-panel"
                            style="background: #e6fff2; border: 1px solid #b2f5ea; padding: 24px; border-radius: 12px; margin-bottom: 24px; text-align: center;">
                            <span class="success-title" style="font-size: 1.5rem; font-weight: bold; color: #38a169;">تم تقديم الطلب بنجاح / Order Submitted Successfully</span>
                            <div class="order-details" style="margin-top: 12px;">
                                رقم الطلب / Order ID:
                                <span class="order-number"
                                    style="font-weight: bold; color: #3182ce;">@TempData["OrderId"]</span>
                                <br />
                                <span class="success-note" style="color: #2d3748;">تم تقديم الطلب بنجاح وتم توجيهه إلى مدير القسم / Order has been successfully submitted and forwarded to the department manager</span>
                            </div>
                        </div>
                    }

                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            @TempData["SuccessMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            @TempData["ErrorMessage"]
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <form asp-action="New" method="post" enctype="multipart/form-data" id="orderForm">
                        <div asp-validation-summary="ModelOnly" class="alert alert-danger"></div>

                        <div class="row g-4">
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">اسم الموظف / Employee Name <span
                                        class="text-danger">*</span></label>
                                <input asp-for="EmployeeName" class="form-control form-control-modern" required />
                                <span asp-validation-for="EmployeeName" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">الوظيفة / Job Title <span class="text-danger">*</span></label>
                                <div class="autocomplete-wrapper">
                                    <input asp-for="JobTitle" class="form-control form-control-modern autocomplete-field"
                                           data-source="JobTitles" placeholder="اكتب أو اختر الوظيفة / Type or select job title" required />
                                    <i class="fas fa-chevron-down autocomplete-arrow"></i>
                                </div>
                                <span asp-validation-for="JobTitle" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">رقم الموظف / Employee Number <span
                                        class="text-danger">*</span></label>
                                <input asp-for="EmployeeNumber" class="form-control form-control-modern" required />
                                <span asp-validation-for="EmployeeNumber" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">السجل المدني / Civil Record <span
                                        class="text-danger">*</span></label>
                                <input asp-for="CivilRecord" class="form-control form-control-modern" required />
                                <span asp-validation-for="CivilRecord" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">الجنسية / Nationality <span class="text-danger">*</span></label>
                                <div class="autocomplete-wrapper">
                                    <input asp-for="Nationality" class="form-control form-control-modern autocomplete-field"
                                           data-source="Nationalities" placeholder="اكتب أو اختر الجنسية / Type or select nationality" required />
                                    <i class="fas fa-chevron-down autocomplete-arrow"></i>
                                </div>
                                <span asp-validation-for="Nationality" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">رقم الجوال / Mobile Number <span
                                        class="text-danger">*</span></label>
                                <input asp-for="MobileNumber" class="form-control form-control-modern" required
                                    placeholder="+966 5X XXX XXXX" />
                                <span asp-validation-for="MobileNumber" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">نوع التوظيف / Employment Type <span
                                        class="text-danger">*</span></label>
                                <div class="autocomplete-wrapper">
                                    <input asp-for="EmploymentType" class="form-control form-control-modern autocomplete-field"
                                           data-source="EmploymentTypes" placeholder="اكتب أو اختر نوع التوظيف / Type or select employment type" required />
                                    <i class="fas fa-chevron-down autocomplete-arrow"></i>
                                </div>
                                <span asp-validation-for="EmploymentType" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">المؤهل العلمي / Qualification <span
                                        class="text-danger">*</span></label>
                                <div class="autocomplete-wrapper">
                                    <input asp-for="Qualification" class="form-control form-control-modern autocomplete-field"
                                           data-source="Qualifications" placeholder="اكتب أو اختر المؤهل / Type or select qualification" required />
                                    <i class="fas fa-chevron-down autocomplete-arrow"></i>
                                </div>
                                <span asp-validation-for="Qualification" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">اختر المسؤول / Select Manager <span class="text-danger">*</span></label>
                                <select asp-for="AssistantManagerId" asp-items="Model.AssistantManagers"
                                    class="form-select form-select-modern" required id="assistantManagerSelect">
                                    <option value="">-- اختر المسؤول / Select Manager --</option>
                                </select>
                                <span asp-validation-for="AssistantManagerId" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">القسم / Department <span class="text-danger">*</span></label>
                                <select asp-for="Department" class="form-select form-select-modern" required id="departmentSelect" disabled>
                                    <option value="">-- اختر القسم / Select Department --</option>
                                </select>
                                <span asp-validation-for="Department" class="text-danger"></span>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label-modern-dark">نوع الطلب / Order Type <span
                                        class="text-danger">*</span></label>
                                <select asp-for="OrderType" asp-items="Model.OrderTypes"
                                    class="form-select form-select-modern" required>
                                    <option value="">-- اختر نوع الطلب / Select Order Type --</option>
                                </select>
                                <span asp-validation-for="OrderType" class="text-danger"></span>
                            </div>
                            <div class="col-12">
                                <label class="form-label-modern-dark">تفاصيل الطلب / Order Details</label>
                                <textarea asp-for="Details" class="form-control form-control-modern" rows="4"
                                    placeholder="اكتب تفاصيل طلبك هنا... / Write your order details here..."></textarea>
                            </div>
                            <div class="col-12">
                                <label class="form-label-modern-dark">المرفقات / Attachments</label>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <label class="form-label-modern-dark small">المرفق الأول / First Attachment <span
                                                class="text-danger">*</span></label>
                                        <input type="file" class="form-control form-control-modern" accept=".pdf"
                                            name="Attachments" required />
                                        <div class="form-text small">ملف PDF / PDF File</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label-modern-dark small">المرفق الثاني / Second Attachment</label>
                                        <input type="file" class="form-control form-control-modern" accept=".pdf"
                                            name="Attachments" />
                                        <div class="form-text small">ملف PDF / PDF File</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label-modern-dark small">المرفق الثالث / Third Attachment</label>
                                        <input type="file" class="form-control form-control-modern" accept=".pdf"
                                            name="Attachments" />
                                        <div class="form-text small">ملف PDF / PDF File</div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label-modern-dark small">المرفق الرابع / Fourth Attachment</label>
                                        <input type="file" class="form-control form-control-modern" accept=".pdf"
                                            name="Attachments" />
                                        <div class="form-text small">ملف PDF / PDF File</div>
                                    </div>
                                </div>
                                <div class="form-text mt-2">يمكن رفع ملفات PDF فقط، الحد الأقصى 4 ملفات / Only PDF files can be uploaded, maximum 4 files</div>
                            </div>
                            <div class="col-12 text-center mt-5">
                                <button type="submit" class="btn btn-send">
                                    <i class="fas fa-paper-plane me-2"></i>تقديم الطلب / Submit Order
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Information Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5">
            <span class="badge bg-primary bg-opacity-10 text-primary px-3 py-2 mb-3">معلومات مهمة / Important Information</span>
            <h2 class="display-6 fw-bold">تعليمات تقديم الطلب / Order Submission Instructions</h2>
            <p class="text-muted">يرجى قراءة التعليمات التالية قبل تقديم طلبك / Please read the following instructions before submitting your order</p>
        </div>
        <div class="row g-4">
            <div class="col-md-4">
                <div class="contact-card text-center">
                    <div class="contact-card-icon" style="background: rgba(8, 145, 178, 0.1);">
                        <i class="fas fa-file-alt" style="color: var(--primary-color);"></i>
                    </div>
                    <h5 class="fw-bold mb-3">المستندات المطلوبة / Required Documents</h5>
                    <p class="text-muted">تأكد من إرفاق جميع المستندات المطلوبة بصيغة PDF / Make sure to attach all required documents in PDF format</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="contact-card text-center">
                    <div class="contact-card-icon" style="background: rgba(16, 185, 129, 0.1);">
                        <i class="fas fa-clock" style="color: var(--success);"></i>
                    </div>
                    <h5 class="fw-bold mb-3">وقت المعالجة / Processing Time</h5>
                    <p class="text-muted">يتم معالجة الطلبات خلال 2-5 أيام عمل / Orders are processed within 2-5 business days</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="contact-card text-center">
                    <div class="contact-card-icon" style="background: rgba(245, 158, 11, 0.1);">
                        <i class="fas fa-bell" style="color: var(--warning);"></i>
                    </div>
                    <h5 class="fw-bold mb-3">المتابعة / Follow-up</h5>
                    <p class="text-muted">ستتلقى إشعارات بكل تحديث على حالة طلبك / You will receive notifications for every update on your order status</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Back to Home -->
<section class="py-4 bg-light">
    <div class="container text-center">
        <a href="@Url.Action("Index", "Home")" class="btn btn-modern btn-primary-modern"
            style="background: var(--primary-color); color: white;">
            <i class="fas fa-arrow-right me-2"></i>العودة للصفحة الرئيسية / Back to Home Page
        </a>
    </div>
</section>



@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }

    <!-- jQuery UI for autocomplete -->
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/ui-lightness/jquery-ui.css">
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>

    <!-- Custom CSS for autocomplete styling -->
    <style>
        .custom-autocomplete {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            background: white;
            z-index: 1000;
            font-family: 'Cairo', sans-serif;
            overflow-x: hidden;
        }

        .custom-autocomplete .ui-menu-item {
            padding: 0;
            margin: 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .custom-autocomplete .ui-menu-item:last-child {
            border-bottom: none;
        }

        .custom-autocomplete .ui-menu-item-wrapper {
            padding: 10px 15px;
            color: #333;
            text-decoration: none;
            display: block;
            cursor: pointer;
        }

        .custom-autocomplete .ui-menu-item-wrapper:hover,
        .custom-autocomplete .ui-state-active {
             border: 1px solid var(--primary-color) !important;
             background-color: var(--primary-color) !important;
        }

        .autocomplete-wrapper {
            position: relative;
        }

        .autocomplete-field {
            padding-right: 35px;
        }

        .autocomplete-field:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(8, 145, 178, 0.25);
        }

        .autocomplete-arrow {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #6c757d;
            pointer-events: auto;
            font-size: 12px;
            transition: transform 0.2s ease;
            cursor: pointer;
            z-index: 10;
        }

        .autocomplete-wrapper:hover .autocomplete-arrow {
            color: var(--primary-color);
        }

        .autocomplete-field:focus + .autocomplete-arrow {
            transform: translateY(-50%) rotate(180deg);
            color: var(--primary-color);
        }
    </style>

    <script>
        // Dropdown data for autocomplete
        var dropdownData = {
            JobTitles: @Html.Raw(Json.Serialize(Model.JobTitles.Select(x => x.Text).ToList())),
            Nationalities: @Html.Raw(Json.Serialize(Model.Nationalities.Select(x => x.Text).ToList())),
            EmploymentTypes: @Html.Raw(Json.Serialize(Model.EmploymentTypes.Select(x => x.Text).ToList())),
            Qualifications: @Html.Raw(Json.Serialize(Model.Qualifications.Select(x => x.Text).ToList()))
        };

        // Initialize autocomplete fields
        $(document).ready(function() {
            $('.autocomplete-field').each(function() {
                var $field = $(this);
                var source = $field.data('source');
                var data = dropdownData[source] || [];

                $field.autocomplete({
                    source: function(request, response) {
                        // Filter data based on input
                        var filtered = data.filter(function(item) {
                            return item.toLowerCase().indexOf(request.term.toLowerCase()) !== -1;
                        });
                        response(filtered);
                    },
                    minLength: 0,
                    autoFocus: true,
                    classes: {
                        "ui-autocomplete": "custom-autocomplete"
                    },
                    select: function(event, ui) {
                        var value = ui.item.value;
                        $(this).val(value);
                        $(this).trigger('change');
                        return false;
                    }
                    
                }).on('click', function() {
                    // Show dropdown on click
                    $(this).autocomplete('search', $(this).val());
                });

                // Make arrow clickable
                $field.siblings('.autocomplete-arrow').on('click', function() {
                    var $input = $(this).siblings('.autocomplete-field');
                    $input.focus();
                    $input.autocomplete('search', $input.val());
                });
            });
        });

        // Form validation and submission
        document.getElementById('orderForm').addEventListener('submit', function (e) {
            // Add validation classes
            if (!this.checkValidity()) {
                e.stopPropagation();
                this.classList.add('was-validated');
                return;
            }

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التقديم... / Submitting...';
            submitBtn.disabled = true;
        });

        // Phone number formatting
        const phoneInput = document.querySelector('input[asp-for="MobileNumber"]');
        if (phoneInput) {
            phoneInput.addEventListener('input', function (e) {
                let value = e.target.value.replace(/\s/g, '');
                let formattedValue = '';

                if (value.startsWith('+966')) {
                    if (value.length > 4) formattedValue += value.substring(0, 4) + ' ';
                    if (value.length > 5) formattedValue += value.substring(4, 6) + ' ';
                    if (value.length > 6) formattedValue += value.substring(6, 9) + ' ';
                    if (value.length > 9) formattedValue += value.substring(9, 13);
                } else {
                    formattedValue = value;
                }

                e.target.value = formattedValue.trim();
            });
        }

        // Floating shapes parallax
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const shapes = document.querySelectorAll('.floating-shape');

            shapes.forEach((shape, index) => {
                const speed = 0.5 + (index * 0.2);
                shape.style.transform = `translateY(${scrolled * speed}px) rotate(${scrolled * 0.1}deg)`;
            });
        });

        // Contact cards interactive effect
        const contactCards = document.querySelectorAll('.contact-card');
        contactCards.forEach(card => {
            card.addEventListener('mousemove', function (e) {
                const rect = this.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                const centerX = rect.width / 2;
                const centerY = rect.height / 2;

                const rotateX = (y - centerY) / 10;
                const rotateY = (centerX - x) / 10;

                this.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
            });

            card.addEventListener('mouseleave', function () {
                this.style.transform = '';
            });
        }); 

        // Employee search functionality
        const searchBtn = document.getElementById('searchEmployeeBtn');
        const searchInput = document.getElementById('searchCivilRecord');
        const searchResult = document.getElementById('searchResult');

        if (searchBtn && searchInput && searchResult) {
            searchBtn.addEventListener('click', searchEmployee);
            searchInput.addEventListener('keypress', function (e) {
                if (e.key === 'Enter') {
                    searchEmployee();
                }
            });
        }

        function searchEmployee() {
            const civilRecord = searchInput.value.trim();
            if (!civilRecord) {
                showSearchResult('يرجى إدخال السجل المدني / Please enter civil record', 'warning');
                return;
            }

            // Show loading state
            searchBtn.disabled = true;
            searchBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري البحث... / Searching...';
            showSearchResult('جاري البحث عن الموظف... / Searching for employee...', 'info');

            // Make AJAX request
            fetch(`/Order/SearchEmployee?civilRecord=${encodeURIComponent(civilRecord)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        populateFormFields(data.employee);
                        showSearchResult('تم العثور على الموظف بنجاح! / Employee found successfully!', 'success');
                    } else {
                        showSearchResult(data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showSearchResult('حدث خطأ أثناء البحث / An error occurred during search', 'danger');
                })
                .finally(() => {
                    // Reset button state
                    searchBtn.disabled = false;
                    searchBtn.innerHTML = '<i class="fas fa-search me-2"></i>بحث / Search';
                });
        }

        function populateFormFields(employee) {
            // Populate form fields with employee data
            const fields = {
                'EmployeeName': employee.name,
                'JobTitle': employee.job,
                'EmployeeNumber': employee.employeeNumber,
                'CivilRecord': employee.civilNumber,
                'Nationality': employee.nationality,
                'MobileNumber': employee.mobile,
                'EmploymentType': employee.employmentType,
                'Qualification': employee.qualification,
            };

            Object.keys(fields).forEach(fieldName => {
                const field = document.getElementById(fieldName);
                if (field && fields[fieldName]) {
                    field.value = fields[fieldName];
                    // Trigger change event for validation
                    field.dispatchEvent(new Event('change', { bubbles: true }));
                    // Close autocomplete if it's open
                    if ($(field).hasClass('autocomplete-field')) {
                        $(field).autocomplete('close');
                    }
                }
            });
        }

        function showSearchResult(message, type) {
            const alertClass = type === 'success' ? 'alert-success' :
                type === 'warning' ? 'alert-warning' :
                    type === 'info' ? 'alert-info' : 'alert-danger';

            const iconClass = type === 'success' ? 'check-circle' :
                type === 'warning' ? 'exclamation-triangle' :
                    type === 'info' ? 'info-circle' : 'times-circle';

            searchResult.innerHTML = `
                    <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                        <i class="fas fa-${iconClass} me-2"></i>
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;
        }

        // Cascading dropdown functionality for Assistant Manager -> Department
        $(document).ready(function() {
            const assistantManagerSelect = $('#assistantManagerSelect');
            const departmentSelect = $('#departmentSelect');

            assistantManagerSelect.on('change', function() {
                const selectedManagerType = $(this).val();

                // Clear and disable department dropdown
                departmentSelect.empty().append('<option value="">-- اختر القسم / Select Department --</option>');

                if (!selectedManagerType || selectedManagerType === '') {
                    departmentSelect.prop('disabled', true);
                    return;
                }

                // Show loading state
                departmentSelect.prop('disabled', true);
                departmentSelect.append('<option value="">جاري التحميل... / Loading...</option>');

                // Fetch departments for selected manager
                $.ajax({
                    url: '@Url.Action("GetDepartmentsByManager", "Order")',
                    type: 'GET',
                    data: { assistantManagerType: selectedManagerType },
                    success: function(response) {
                        departmentSelect.empty().append('<option value="">-- اختر القسم / Select Department --</option>');

                        if (response.success && response.departments && response.departments.length > 0) {
                            $.each(response.departments, function(index, dept) {
                                departmentSelect.append('<option value="' + dept.value + '">' + dept.text + '</option>');
                            });
                            departmentSelect.prop('disabled', false);
                        } else {
                            departmentSelect.append('<option value="">لا توجد أقسام متاحة / No departments available</option>');
                        }
                    },
                    error: function() {
                        departmentSelect.empty().append('<option value="">خطأ في تحميل الأقسام / Error loading departments</option>');
                    }
                });
            });
        });
    </script>
}