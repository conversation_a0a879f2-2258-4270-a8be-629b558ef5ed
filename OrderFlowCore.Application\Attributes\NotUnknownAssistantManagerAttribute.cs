using OrderFlowCore.Core.Entities;
using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Application.Attributes;

public class NotUnknownAssistantManagerAttribute : ValidationAttribute
{
    public NotUnknownAssistantManagerAttribute() : base("يجب اختيار المسؤول")
    {
    }

    public override bool IsValid(object? value)
    {
        if (value is AssistantManagerType assistantManagerType)
        {
            return assistantManagerType != AssistantManagerType.Unknown;
        }
        return false;
    }
}
